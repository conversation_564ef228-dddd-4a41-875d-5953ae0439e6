"""
Test Database Fix

This script tests that the database migration fixes work correctly.
"""

import os
import sys
import sqlite3

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_direct_database_fix():
    """Test direct database fix without importing the full module"""
    print("\n🔍 Testing Direct Database Fix")
    print("=" * 60)
    
    try:
        # Get the database path
        db_path = os.path.join(os.path.dirname(__file__), 'backend', 'src', 'power_plants.db')
        
        # Connect directly to SQLite
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if plant_uuid column exists
        cursor.execute("PRAGMA table_info(power_plants_registry)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'plant_uuid' not in columns:
            print("🔄 Adding plant_uuid column...")
            
            # Add the column
            cursor.execute("ALTER TABLE power_plants_registry ADD COLUMN plant_uuid TEXT")
            
            # Create index
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_plant_uuid ON power_plants_registry(plant_uuid)")
            
            # Populate existing records with UUIDs
            import uuid
            cursor.execute("SELECT id, plant_name FROM power_plants_registry WHERE plant_uuid IS NULL OR plant_uuid = ''")
            records = cursor.fetchall()
            
            for record_id, plant_name in records:
                plant_uuid = str(uuid.uuid4())
                cursor.execute("UPDATE power_plants_registry SET plant_uuid = ? WHERE id = ?", (plant_uuid, record_id))
                print(f"✅ Generated UUID for {plant_name}: {plant_uuid}")
            
            conn.commit()
            print("✅ Database migration completed successfully")
        else:
            print("✅ plant_uuid column already exists")
        
        # Test that we can query the column
        cursor.execute("SELECT COUNT(*) FROM power_plants_registry")
        count = cursor.fetchone()[0]
        print(f"✅ Database query successful - {count} records found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Direct database fix failed: {e}")
        return False

def test_database_schema():
    """Test that the database schema is correct"""
    print("\n🔍 Testing Database Schema")
    print("=" * 60)
    
    try:
        # Get the database path
        db_path = os.path.join(os.path.dirname(__file__), 'backend', 'src', 'power_plants.db')
        
        # Connect directly to SQLite
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get table schema
        cursor.execute("PRAGMA table_info(power_plants_registry)")
        columns = cursor.fetchall()
        
        print("📋 Current table schema:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # Check for required columns
        column_names = [col[1] for col in columns]
        required_columns = ['id', 'org_name', 'plant_name', 'country', 'org_uid', 'plant_uuid']
        
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present")
            return True
        
    except Exception as e:
        print(f"❌ Database schema test failed: {e}")
        return False

def test_create_test_record():
    """Test creating a test record with plant_uuid"""
    print("\n🔍 Testing Test Record Creation")
    print("=" * 60)
    
    try:
        # Get the database path
        db_path = os.path.join(os.path.dirname(__file__), 'backend', 'src', 'power_plants.db')
        
        # Connect directly to SQLite
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Generate test data
        import uuid
        from datetime import datetime
        
        test_org_name = "Test Organization"
        test_plant_name = "Test Plant"
        test_country = "Test Country"
        test_org_uid = f"ORG_TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_plant_uuid = str(uuid.uuid4())
        
        # Insert test record
        cursor.execute("""
            INSERT INTO power_plants_registry 
            (org_name, plant_name, country, org_uid, plant_uuid, created_at) 
            VALUES (?, ?, ?, ?, ?, ?)
        """, (test_org_name, test_plant_name, test_country, test_org_uid, test_plant_uuid, datetime.now()))
        
        conn.commit()
        
        # Verify the record was created
        cursor.execute("SELECT * FROM power_plants_registry WHERE plant_uuid = ?", (test_plant_uuid,))
        record = cursor.fetchone()
        
        if record:
            print(f"✅ Test record created successfully:")
            print(f"  - Org Name: {record[1]}")
            print(f"  - Plant Name: {record[2]}")
            print(f"  - Country: {record[3]}")
            print(f"  - Org UID: {record[4]}")
            print(f"  - Plant UUID: {record[5]}")
            
            # Clean up - delete test record
            cursor.execute("DELETE FROM power_plants_registry WHERE plant_uuid = ?", (test_plant_uuid,))
            conn.commit()
            print("✅ Test record cleaned up")
            
            conn.close()
            return True
        else:
            print("❌ Test record not found after creation")
            conn.close()
            return False
        
    except Exception as e:
        print(f"❌ Test record creation failed: {e}")
        return False

def main():
    """Run all database fix tests"""
    print("🚀 Testing Database Fixes")
    print("=" * 80)
    
    tests = [
        ("Direct Database Fix", test_direct_database_fix),
        ("Database Schema", test_database_schema),
        ("Test Record Creation", test_create_test_record)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 DATABASE FIX TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 DATABASE FIXES SUCCESSFUL!")
        print("\n💡 What was fixed:")
        print("1. ✅ plant_uuid column added to database")
        print("2. ✅ Index created for plant_uuid column")
        print("3. ✅ Existing records populated with UUIDs")
        print("4. ✅ Database schema is correct")
        print("5. ✅ Can create records with plant_uuid")
        
        print("\n🚀 Expected behavior:")
        print("- No more 'no such column: plant_uuid' errors")
        print("- UID generation should work")
        print("- Plant registry checks should succeed")
        print("- Database operations should complete")
        
        print("\n🎯 Database is ready for the application!")
    else:
        print(f"\n⚠️ {total - passed} database fixes need attention")
        print("\n🔧 Manual fix:")
        print("1. Delete the database file: backend/src/power_plants.db")
        print("2. Restart the application to recreate with correct schema")

if __name__ == "__main__":
    main()
