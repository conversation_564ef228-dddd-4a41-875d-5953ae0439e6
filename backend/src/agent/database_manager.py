"""
Power Plant Registry Database Manager

This module handles the database operations for the power plant registry system.
Supports both SQLite (development) and PostgreSQL (production) through SQLAlchemy.
"""

import os
import hashlib
import time
import uuid
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, Index, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
import enum

# Database configuration
Base = declarative_base()

class PlantStatus(enum.Enum):
    OPERATIONAL = "operational"
    UNDER_CONSTRUCTION = "under_construction"
    DECOMMISSIONED = "decommissioned"
    RETIRED = "retired"
    UNKNOWN = "unknown"

class DiscoveryStatus(enum.Enum):
    PARTIAL = "partial"  # Only basic org info discovered
    COMPLETE = "complete"  # Full plant list discovered
    FAILED = "failed"  # Discovery failed

class PowerPlantRegistry(Base):
    """
    Database model for power plant registry

    This table stores information about power plants and their parent organizations.
    Each organization gets a unique UID, and all plants under that organization
    share the same org_uid. Each plant also gets its own unique plant_uuid.
    """
    __tablename__ = 'power_plants_registry'

    id = Column(Integer, primary_key=True, autoincrement=True)
    org_name = Column(String(255), nullable=False, index=True)
    plant_name = Column(String(255), nullable=False)
    country = Column(String(100), nullable=False)
    org_uid = Column(String(50), nullable=False, index=True)
    plant_uuid = Column(String(36), nullable=False, index=True, unique=True)  # UUID for individual plants
    plant_status = Column(String(20), default="operational")  # Use string instead of enum
    discovery_status = Column(String(10), default="partial")  # Use string instead of enum
    discovery_session_id = Column(String(50), nullable=True)
    discovered_from_plant = Column(String(255), nullable=True)  # Which plant triggered the discovery
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Indexes for performance
    __table_args__ = (
        Index('idx_plant_country', 'plant_name', 'country'),
        Index('idx_org_uid', 'org_uid'),
        Index('idx_org_name', 'org_name'),
        Index('idx_plant_uuid', 'plant_uuid'),
    )

class DatabaseManager:
    """
    Database manager for power plant registry operations
    
    Handles database connections, CRUD operations, and UID generation.
    Supports both SQLite (development) and PostgreSQL (production).
    """
    
    def __init__(self, database_url: Optional[str] = None):
        """
        Initialize database manager
        
        Args:
            database_url: Database connection URL. If None, uses environment variable
                         or defaults to SQLite for development
        """
        if database_url is None:
            database_url = self._get_database_url()
        
        self.database_url = database_url
        self.engine = create_engine(
            database_url,
            echo=False,  # Set to True for SQL debugging
            pool_pre_ping=True,  # Verify connections before use
        )
        
        # Configure connection pooling for PostgreSQL
        if 'postgresql' in database_url:
            self.engine = create_engine(
                database_url,
                echo=False,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
            )
        
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.db_type = "sqlite" if "sqlite" in database_url else "postgresql"
        
        # Create tables if they don't exist
        self.create_tables()
    
    def _get_database_url(self) -> str:
        """
        Get database URL based on environment
        
        Returns:
            Database connection URL
        """
        env = os.getenv("ENVIRONMENT", "development")
        
        if env == "development":
            # SQLite for development
            db_path = os.path.join(os.path.dirname(__file__), "powerplant_registry.db")
            return f"sqlite:///{db_path}"
        elif env == "testing":
            # In-memory SQLite for testing
            return "sqlite:///:memory:"
        else:
            # PostgreSQL for production
            db_url = os.getenv("DATABASE_URL")
            if not db_url:
                raise ValueError("DATABASE_URL environment variable is required for production")
            return db_url
    
    def create_tables(self):
        """Create database tables if they don't exist and run migrations"""
        try:
            # First, create tables if they don't exist
            Base.metadata.create_all(bind=self.engine)
            print(f"✅ Database tables created/verified ({self.db_type})")

            # Run migrations for existing databases
            self._run_migrations()

        except Exception as e:
            print(f"❌ Error creating database tables: {e}")
            raise

    def _run_migrations(self):
        """Run database migrations for schema updates"""
        try:
            # Check if plant_uuid column exists
            if not self._column_exists('power_plants_registry', 'plant_uuid'):
                print("🔄 Running migration: Adding plant_uuid column")
                self._add_plant_uuid_column()
            else:
                print("✅ Migration check: plant_uuid column already exists")

        except Exception as e:
            print(f"❌ Migration error: {e}")

    def _column_exists(self, table_name: str, column_name: str) -> bool:
        """Check if a column exists in a table"""
        try:
            if self.db_type == "sqlite":
                # For SQLite, use PRAGMA table_info
                result = self.engine.execute(f"PRAGMA table_info({table_name})")
                columns = [row[1] for row in result]  # Column names are in index 1
                return column_name in columns
            else:
                # For PostgreSQL, use information_schema
                result = self.engine.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = %s AND column_name = %s
                """, (table_name, column_name))
                return result.fetchone() is not None
        except Exception as e:
            print(f"❌ Error checking column existence: {e}")
            return False

    def _add_plant_uuid_column(self):
        """Add plant_uuid column to existing power_plants_registry table"""
        try:
            if self.db_type == "sqlite":
                # SQLite doesn't support ADD COLUMN with constraints directly
                # We need to add the column first, then populate it
                self.engine.execute("ALTER TABLE power_plants_registry ADD COLUMN plant_uuid TEXT")
                print("✅ Added plant_uuid column to power_plants_registry")

                # Create index for the new column
                self.engine.execute("CREATE INDEX IF NOT EXISTS idx_plant_uuid ON power_plants_registry(plant_uuid)")
                print("✅ Created index for plant_uuid column")

                # Populate existing records with UUIDs
                self._populate_plant_uuids()

            else:
                # PostgreSQL supports ADD COLUMN with constraints
                self.engine.execute("""
                    ALTER TABLE power_plants_registry
                    ADD COLUMN plant_uuid VARCHAR(36) UNIQUE
                """)
                self.engine.execute("CREATE INDEX idx_plant_uuid ON power_plants_registry(plant_uuid)")
                print("✅ Added plant_uuid column with constraints to power_plants_registry")

                # Populate existing records with UUIDs
                self._populate_plant_uuids()

        except Exception as e:
            print(f"❌ Error adding plant_uuid column: {e}")
            raise

    def _populate_plant_uuids(self):
        """Populate plant_uuid for existing records that don't have it"""
        session = self.get_session()
        try:
            # Find records without plant_uuid
            records_without_uuid = session.query(PowerPlantRegistry).filter(
                (PowerPlantRegistry.plant_uuid == None) |
                (PowerPlantRegistry.plant_uuid == "")
            ).all()

            updated_count = 0
            for record in records_without_uuid:
                # Generate unique UUID for each plant
                plant_uuid = str(uuid.uuid4())
                record.plant_uuid = plant_uuid
                updated_count += 1
                print(f"✅ Generated UUID for {record.plant_name}: {plant_uuid}")

            session.commit()
            print(f"✅ Populated plant_uuid for {updated_count} existing records")

        except Exception as e:
            session.rollback()
            print(f"❌ Error populating plant_uuids: {e}")
            raise
        finally:
            session.close()
    
    def get_session(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    def generate_org_uid(self, org_name: str, country: str) -> str:
        """
        Generate unique organization UID

        Format: ORG_{COUNTRY_CODE}_{ORG_HASH}_{TIMESTAMP}
        Example: ORG_IN_A7B2C9_20241201

        Args:
            org_name: Organization name
            country: Country name

        Returns:
            Unique organization UID
        """
        # Get country code (first 2 letters, uppercase)
        country_code = country[:2].upper()

        # Create hash from organization name
        org_hash = hashlib.sha256(org_name.lower().encode()).hexdigest()[:6].upper()

        # Add timestamp for uniqueness
        timestamp = str(int(time.time()))[-8:]  # Last 8 digits

        org_uid = f"ORG_{country_code}_{org_hash}_{timestamp}"

        # Ensure uniqueness (very rare collision case)
        session = self.get_session()
        try:
            existing = session.query(PowerPlantRegistry).filter_by(org_uid=org_uid).first()
            if existing:
                # Add random suffix if collision occurs
                import random
                suffix = str(random.randint(1000, 9999))
                org_uid = f"{org_uid}_{suffix}"

            return org_uid
        finally:
            session.close()

    def generate_plant_uuid(self) -> str:
        """
        Generate unique plant UUID

        Returns:
            Unique plant UUID (standard UUID4 format)
        """
        plant_uuid = str(uuid.uuid4())

        # Ensure uniqueness (very rare collision case for UUID4)
        session = self.get_session()
        try:
            existing = session.query(PowerPlantRegistry).filter_by(plant_uuid=plant_uuid).first()
            if existing:
                # Generate a new UUID if collision occurs (extremely rare)
                plant_uuid = str(uuid.uuid4())

            return plant_uuid
        finally:
            session.close()
    
    def check_plant_exists(self, plant_name: str, country: str = None) -> Optional[Dict]:
        """
        Check if plant exists in database
        
        Args:
            plant_name: Name of the power plant
            country: Country name (optional for more specific search)
            
        Returns:
            Plant information dict if found, None otherwise
        """
        session = self.get_session()
        try:
            # Clean the plant name to handle whitespace and formatting issues
            clean_plant_name = plant_name.strip() if plant_name else ""
            
            query = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.plant_name.ilike(f"%{clean_plant_name}%")
            )
            
            if country:
                query = query.filter(PowerPlantRegistry.country.ilike(f"%{country}%"))
            
            plant = query.first()
            
            if plant:
                return {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uuid": plant.plant_uuid,
                    "plant_status": plant.plant_status,
                    "discovery_status": plant.discovery_status,
                    "created_at": plant.created_at,
                }
            
            return None
            
        except Exception as e:
            print(f"❌ Error checking plant existence: {e}")
            return None
        finally:
            session.close()
    
    def save_organization_plants(
        self, 
        org_name: str, 
        country: str, 
        plants_list: List[Dict], 
        org_uid: str,
        discovery_session_id: str,
        discovered_from_plant: str
    ) -> bool:
        """
        Save all plants from an organization to database
        
        Args:
            org_name: Organization name
            country: Country name
            plants_list: List of plant dictionaries
            org_uid: Organization unique ID
            discovery_session_id: Session ID that triggered discovery
            discovered_from_plant: Plant name that triggered the discovery
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            plants_saved = 0
            
            for plant_info in plants_list:
                plant_name = plant_info.get("name", "").strip()
                if not plant_name:
                    continue
                
                # Check if plant already exists
                existing = session.query(PowerPlantRegistry).filter_by(
                    plant_name=plant_name,
                    org_name=org_name
                ).first()
                
                if existing:
                    # Update existing record
                    existing.org_uid = org_uid
                    existing.discovery_status = "complete"
                    existing.updated_at = datetime.utcnow()
                    # Generate plant_uuid if it doesn't exist (for existing records)
                    if not existing.plant_uuid:
                        existing.plant_uuid = self.generate_plant_uuid()
                else:
                    # Create new record with plant UUID
                    plant_uuid = self.generate_plant_uuid()
                    plant_record = PowerPlantRegistry(
                        org_name=org_name,
                        plant_name=plant_name,
                        country=country,
                        org_uid=org_uid,
                        plant_uuid=plant_uuid,
                        plant_status=plant_info.get("status", "operational"),
                        discovery_status="complete",
                        discovery_session_id=discovery_session_id,
                        discovered_from_plant=discovered_from_plant
                    )
                    session.add(plant_record)
                    print(f"✅ Generated plant UUID for {plant_name}: {plant_uuid}")
                
                plants_saved += 1
            
            session.commit()
            print(f"✅ Saved {plants_saved} plants for organization: {org_name}")
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error saving organization plants: {e}")
            return False
        finally:
            session.close()
    
    def get_plants_by_organization(self, org_name: str) -> List[Dict]:
        """
        Get all plants for a given organization
        
        Args:
            org_name: Organization name
            
        Returns:
            List of plant information dictionaries
        """
        session = self.get_session()
        try:
            plants = session.query(PowerPlantRegistry).filter_by(org_name=org_name).all()
            
            return [
                {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uuid": plant.plant_uuid,
                    "plant_status": plant.plant_status,
                    "discovery_status": plant.discovery_status,
                    "created_at": plant.created_at,
                }
                for plant in plants
            ]
            
        except Exception as e:
            print(f"❌ Error getting plants by organization: {e}")
            return []
        finally:
            session.close()

    def get_plants_by_organization_uid(self, org_uid: str) -> List[Dict]:
        """
        Get all plants for a given organization UID

        Args:
            org_uid: Organization unique identifier

        Returns:
            List of plant information dictionaries
        """
        session = self.get_session()
        try:
            plants = session.query(PowerPlantRegistry).filter_by(org_uid=org_uid).all()

            return [
                {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uuid": plant.plant_uuid,
                    "plant_status": plant.plant_status,
                    "discovery_status": plant.discovery_status,
                    "created_at": plant.created_at,
                }
                for plant in plants
            ]
        except Exception as e:
            print(f"❌ Error getting plants by org_uid: {e}")
            return []
        finally:
            session.close()
    
    def test_connection(self) -> bool:
        """
        Test database connection

        Returns:
            True if connection successful, False otherwise
        """
        try:
            session = self.get_session()
            session.execute(text("SELECT 1"))
            session.close()
            print(f"✅ Database connection successful ({self.db_type})")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    def get_plant_s3_metadata(self, plant_name: str) -> Optional[Dict[str, str]]:
        """
        Get S3 storage metadata for a plant including country, org_uuid, and plant_uuid.

        Args:
            plant_name: Name of the power plant

        Returns:
            Dictionary with S3 metadata or None if plant not found

        Example return:
        {
            "country": "India",
            "org_uuid": "ORG_IN_657FE5_51516770",
            "plant_uuid": "550e8400-e29b-41d4-a716-************",
            "org_name": "Adani Power Limited",
            "plant_name": "Mundra Thermal Power Station"
        }
        """
        session = self.get_session()
        try:
            # Query for plant record
            plant_record = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.plant_name.ilike(f"%{plant_name}%")
            ).first()

            if plant_record:
                return {
                    "country": plant_record.country,
                    "org_uuid": plant_record.org_uid,
                    "plant_uuid": plant_record.plant_uuid,
                    "org_name": plant_record.org_name,
                    "plant_name": plant_record.plant_name
                }
            else:
                print(f"❌ Plant '{plant_name}' not found in database")
                return None

        except Exception as e:
            print(f"❌ Error retrieving plant S3 metadata: {e}")
            return None
        finally:
            session.close()

    def get_all_plants_for_org(self, org_uuid: str) -> List[Dict[str, str]]:
        """
        Get all plants belonging to an organization for multi-plant S3 structure.

        Args:
            org_uuid: Organization UID

        Returns:
            List of plant metadata dictionaries
        """
        session = self.get_session()
        try:
            plant_records = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.org_uid == org_uuid
            ).all()

            plants = []
            for record in plant_records:
                plants.append({
                    "country": record.country,
                    "org_uuid": record.org_uid,
                    "plant_uuid": record.plant_uuid,
                    "org_name": record.org_name,
                    "plant_name": record.plant_name
                })

            return plants

        except Exception as e:
            print(f"❌ Error retrieving plants for org {org_uuid}: {e}")
            return []
        finally:
            session.close()

# Global database manager instance
db_manager = None

def get_database_manager() -> DatabaseManager:
    """
    Get global database manager instance (singleton pattern)
    
    Returns:
        DatabaseManager instance
    """
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager