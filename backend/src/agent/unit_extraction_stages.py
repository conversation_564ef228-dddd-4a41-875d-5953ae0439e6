"""
Multi-stage unit data extraction for better API call efficiency and data quality
"""

import os
import json
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

def parse_json_response(content: str, stage_name: str) -> dict:
    """Enhanced JSON parsing with better error handling and logging"""
    print(f"🔍 {stage_name} raw response length: {len(content)} chars")
    print(f"🔍 Response preview: {content[:200]}...")
    
    # Method 1: Try to extract JSON from the response using brackets
    start_idx = content.find('{')
    end_idx = content.rfind('}') + 1
    
    if start_idx != -1 and end_idx != -1:
        try:
            json_str = content[start_idx:end_idx]
            parsed_data = json.loads(json_str)
            print(f"✅ {stage_name} JSON parsed successfully with {len(parsed_data)} fields")
            return parsed_data
        except json.JSONDecodeError as e:
            print(f"⚠️ Failed to parse extracted JSON for {stage_name}: {e}")
    
    # Method 2: Try parsing the whole response as JSON
    try:
        parsed_data = json.loads(content)
        print(f"✅ Full {stage_name} response parsed as JSON with {len(parsed_data)} fields")  
        return parsed_data
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse full response as JSON for {stage_name}: {e}")
        
    # Method 3: Extract JSON using regex as fallback
    import re
    json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    matches = re.findall(json_pattern, content, re.DOTALL)
    
    if matches:
        for match in matches:
            try:
                parsed_data = json.loads(match)
                print(f"✅ {stage_name} JSON extracted via regex with {len(parsed_data)} fields")
                return parsed_data
            except json.JSONDecodeError:
                continue
    
    print(f"❌ All JSON parsing methods failed for {stage_name}")
    return {}


def extract_basic_unit_info(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 1: Extract basic unit identification and capacity information"""
    
    prompt = f"""Extract basic information for Unit {unit_number} from the research data below.

EXTRACTION STRATEGY (use in this order):
1. PRIORITY 1: Look for GEMWiki (gem.wiki) data - this is the most accurate source for capacity and technology
2. PRIORITY 2: Look for Unit {unit_number} specific information in other sources
3. PRIORITY 3: If unit-specific data unavailable, use plant-level data with clear indication
4. PRIORITY 4: Make reasonable estimates based on available plant data

REQUIRED FIELDS:
1. unit_number: "{unit_number}"
2. plant_id: Unique identifier (extract from text or use "0" as fallback)
3. capacity: Unit capacity in MW (CRITICAL: Check GEMWiki first for "Unit {unit_number}" capacity, then look for unit tables)
4. capacity_unit: "MW"
5. technology: Plant technology type (CRITICAL: Check GEMWiki for accurate technology - Ultra Super Critical, Super Critical, Sub Critical, Combined Cycle, etc.)
6. boiler_type: Boiler specification (if available)
7. commencement_date: Operation start date (yyyy-mm-ddThh:mm:ss.msZ format)

Plant Context: {plant_context.get('plant_name', 'Unknown')} ({plant_context.get('plant_technology', 'Unknown')})

Research Data:
{summaries}

INSTRUCTIONS:
- PRIORITIZE GEMWiki (gem.wiki) data for capacity and technology - this is the most reliable source
- Look for unit tables, specifications, and technical details in GEMWiki content
- Extract what's actually available in the data
- If unit-specific capacity not found, divide total plant capacity by number of units
- Use "Not available" only if no relevant information exists
- Provide your best estimate based on available data

Respond ONLY with JSON format:
{{
  "unit_number": "{unit_number}",
  "plant_id": "extracted_or_0",
  "capacity": "estimated_capacity_value",
  "capacity_unit": "MW",
  "technology": "extracted_technology",
  "boiler_type": "extracted_or_Not_available",
  "commencement_date": "extracted_date_or_Not_available"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Basic Info")
        if parsed_data:
            return parsed_data
            
    except Exception as e:
        print(f"❌ Basic info extraction failed: {e}")
        return {
            "unit_number": unit_number,
            "plant_id": "0",
            "capacity": "Not available", 
            "capacity_unit": "MW",
            "technology": "Not available",
            "boiler_type": "Not available",
            "commencement_date": "Not available"
        }


def extract_performance_metrics(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 2: Extract performance and operational metrics with SPECIFIC FIELD TARGETS"""

    prompt = f"""Extract ONLY the performance and operational metrics for Unit {unit_number}:

CRITICAL SPECIFIC FIELDS TO EXTRACT:
1. heat_rate: Amount of heat energy (in Kcal) needed to generate one kWh of electricity
2. heat_rate_unit: "Kcal/kWh" (standard unit)
3. plf: Array of Plant Load Factor data with "value" and "year"
4. PAF: Array of Plant Availability Factor data with "value" and "year"
5. auxiliary_power_consumed: Array of auxiliary power consumption with "value" and "year"
6. gross_power_generation: Array of power generation with "value" and "year"
7. coal_unit_efficiency: Unit-specific efficiency percentage
8. unit_lifetime: Total operational lifetime in years
9. remaining_useful_life: Remaining years of operation

HEAT RATE RESEARCH QUESTION:
- What is the heat rate of Unit {unit_number} at {plant_context.get('plant_name', 'Unknown')}?
- Please provide the heat rate in kilocalories per kilowatt-hour (kCal/kWh)

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}

Research Data:
{summaries}

Respond ONLY with JSON format. Use empty arrays [] for missing time-series data.
Example:
{{
  "heat_rate": "2150",
  "heat_rate_unit": "Kcal/kWh",
  "plf": [{{"value": "75.5", "year": "2023"}}, {{"value": "78.2", "year": "2022"}}],
  "PAF": [{{"value": "85.3", "year": "2023"}}],
  "auxiliary_power_consumed": [],
  "gross_power_generation": [{{"value": "3500", "year": "2023"}}],
  "coal_unit_efficiency": "42.5",
  "unit_lifetime": "25",
  "remaining_useful_life": "15"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Performance Metrics")
        if parsed_data:
            return parsed_data
            
    except Exception as e:
        print(f"❌ Performance metrics extraction failed: {e}")
        return {
            "heat_rate": "Not available",
            "heat_rate_unit": "Kcal/kWh",  # Default unit
            "plf": [],
            "PAF": [],
            "auxiliary_power_consumed": [],
            "gross_power_generation": [],
            "coal_unit_efficiency": "Not available",
            "unit_lifetime": "Not available",
            "remaining_useful_life": "Not available"
        }


def extract_fuel_and_emissions(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 3: Extract fuel composition and emission data with SPECIFIC FIELD TARGETS"""

    prompt = f"""Extract ONLY the fuel composition and emission information for Unit {unit_number}:

CRITICAL SPECIFIC FIELDS TO EXTRACT:
1. annual_operational_hours: Annual operational hours (FIXED VALUE: 8760)
2. blending_percentage_of_biomass: Biomass co-firing blending percentage (FIXED VALUE: 0.15)
3. emission_factor_coking_coal: Emission factor for coking coal in kg CO₂e/kg of fuel
4. emission_factor_gas: Emission factor for natural gas (FIXED VALUE: 2.69)
5. emission_factor_of_gas_unit: Unit for gas emission factor (kg CO₂e/kg)
6. emission_factor_unit: Unit for emission factor (kgCO₂/kWh)
7. fgds_status: Status of Flue Gas Desulfurization System

COAL TYPE AND EMISSION FACTOR RESEARCH:
- What is the primary type of coal (bituminous, sub-bituminous, lignite, anthracite) used at Unit {unit_number}?
- What is the typical emission factor for that coal type in kg CO₂e/kg of coal burned?
- Source of emission factor data (IPCC, IEA, national inventory guidelines)?

FGDS SYSTEM RESEARCH:
- Does Unit {unit_number} have FGDS installed and operational?
- Type of FGDS (wet limestone, dry FGD, seawater FGD)?
- Year of FGDS commissioning?

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format:
{{
  "annual_operational_hours": 8760,
  "blending_percentage_of_biomass": 0.15,
  "emission_factor_coking_coal": "emission_factor_value_with_source",
  "emission_factor_gas": 2.69,
  "emission_factor_of_gas_unit": "kg CO2e/kg",
  "emission_factor_unit": "kgCO_2/kWH",
  "fgds_status": "Fully_installed_operational_or_status",
  "fuel_type": [
    {{"fuel": "Coal", "type": "Bituminous", "years_percentage": {{"2023": "80", "2022": "85"}}}},
    {{"fuel": "Biomass", "type": "Wood chips", "years_percentage": {{"2023": "20", "2022": "15"}}}}
  ],
  "emission_factor": [{{"value": "0.92", "year": "2023"}}],
  "gcv_coal": "5500",
  "gcv_coal_unit": "kCal/kg",
  "gcv_biomass": "4200",
  "gcv_biomass_unit": "kCal/kg",
  "efficiency_loss_biomass_cofiring": "2.5"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Fuel and emissions extraction failed: {e}")
        return {
            "annual_operational_hours": 8760,  # FIXED VALUE
            "blending_percentage_of_biomass": 0.15,  # FIXED VALUE
            "emission_factor_coking_coal": "Not available",
            "emission_factor_gas": 2.69,  # FIXED VALUE
            "emission_factor_of_gas_unit": "kg CO2e/kg",  # Default value
            "emission_factor_unit": "kgCO_2/kWH",  # Default value
            "fgds_status": "Not available",
            "fuel_type": [],
            "emission_factor": [],
            "gcv_coal": "Not available",
            "gcv_coal_unit": "Not available",
            "gcv_biomass": "Not available",
            "gcv_biomass_unit": "Not available",
            "efficiency_loss_biomass_cofiring": "Not available"
        }


def get_country_currency(country: str) -> str:
    """Get the currency code for a country"""
    currency_map = {
        'United States': 'USD',
        'USA': 'USD',
        'US': 'USD',
        'India': 'INR',
        'China': 'CNY',
        'Japan': 'JPY',
        'Germany': 'EUR',
        'United Kingdom': 'GBP',
        'UK': 'GBP',
        'Canada': 'CAD',
        'Australia': 'AUD',
        'Brazil': 'BRL',
        'South Africa': 'ZAR',
        'Indonesia': 'IDR',
        'Thailand': 'THB',
        'Malaysia': 'MYR',
        'Philippines': 'PHP',
        'Vietnam': 'VND',
        'South Korea': 'KRW',
        'Taiwan': 'TWD',
        'Poland': 'PLN',
        'Turkey': 'TRY',
        'Mexico': 'MXN',
        'Chile': 'CLP',
        'Colombia': 'COP',
        'Argentina': 'ARS'
    }

    # Try exact match first
    if country in currency_map:
        return currency_map[country]

    # Try partial match
    country_lower = country.lower()
    for country_key, currency in currency_map.items():
        if country_key.lower() in country_lower or country_lower in country_key.lower():
            return currency

    # Default to USD if country not found
    return 'USD'

def extract_economic_data(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 4: Extract economic and conversion cost data with SPECIFIC FIELD TARGETS"""

    country = plant_context.get('country', 'Unknown')
    currency = get_country_currency(country)
    prompt = f"""Extract ONLY the economic and conversion cost information for Unit {unit_number}:

CRITICAL SPECIFIC FIELDS TO EXTRACT:
1. capex_required_retrofit_biomass: CAPEX for biomass cofiring retrofit using Palm Kernel Shells (PKS)
2. capex_required_retrofit_biomass_unit: Unit for retrofit CAPEX (FIXED VALUE: {currency}/MW)
3. capex_required_renovation_open_cycle: CAPEX for Open Cycle Gas Turbine (OCGT) conversion
4. capex_required_renovation_open_cycle_unit: Unit for OCGT CAPEX (FIXED VALUE: {currency}/MW)
5. capex_required_renovation_closed_cycle: CAPEX for Combined Cycle Gas Turbine (CCGT) conversion
6. capex_required_renovation_closed_cycle_unit: Unit for CCGT CAPEX (FIXED VALUE: {currency}/MW)

RETROFIT COST RESEARCH QUESTIONS:
- What is the average cost per megawatt (MW) of retrofitting Unit {unit_number} into a biomass co-firing plant in {country}, using Palm Kernel Shells (PKS)?
- How much would it cost to retrofit Unit {unit_number} into an Open Cycle Gas Turbine (OCGT) power plant in {country}?
- How much would it cost to retrofit Unit {unit_number} into a Combined Cycle Gas Turbine (CCGT) power plant in {country}?

IMPORTANT INSTRUCTIONS:
- Express costs as full numbers in local currency (e.g., 145000000 IDR not 145 million IDR)
- Include year of estimate if available
- Consider plant size, age, and site conditions in your estimates
- Use country-specific data when available

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {country}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "capex_required_retrofit_biomass": "50000000",
  "capex_required_retrofit_biomass_unit": "{currency}/MW",
  "capex_required_renovation_open_cycle": "200000000",
  "capex_required_renovation_open_cycle_unit": "{currency}/MW",
  "capex_required_renovation_closed_cycle": "350000000",
  "capex_required_renovation_closed_cycle_unit": "{currency}/MW"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Economic data extraction failed: {e}")
        return {
            "capex_required_retrofit_biomass": "Not available",
            "capex_required_retrofit_biomass_unit": f"{currency}/MW",  # FIXED VALUE with actual currency
            "capex_required_renovation_open_cycle": "Not available",
            "capex_required_renovation_open_cycle_unit": f"{currency}/MW",  # FIXED VALUE with actual currency
            "capex_required_renovation_closed_cycle": "Not available",
            "capex_required_renovation_closed_cycle_unit": f"{currency}/MW"  # FIXED VALUE with actual currency
        }


def extract_technical_parameters(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 5: Extract country-specific technical parameters"""
    
    prompt = f"""Extract ONLY the country-specific technical parameters for Unit {unit_number}:

EXTRACT ONLY THESE FIELDS:
1. gcv_natural_gas: Gross calorific value of natural gas
2. gcv_natural_gas_unit: Unit for gas GCV  
3. open_cycle_gas_turbine_efficency: OCGT efficiency for the country
4. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country
5. combined_cycle_heat_rate: CCGT heat rate for the country
6. open_cycle_heat_rate: OCGT heat rate for the country

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "gcv_natural_gas": "9500",
  "gcv_natural_gas_unit": "kCal/m3",
  "open_cycle_gas_turbine_efficency": "35",
  "closed_cylce_gas_turbine_efficency": "55",
  "combined_cycle_heat_rate": "1800",
  "open_cycle_heat_rate": "2800"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Technical parameters extraction failed: {e}")
        return {
            "gcv_natural_gas": "Not available",
            "gcv_natural_gas_unit": "Not available",
            "open_cycle_gas_turbine_efficency": "Not available",
            "closed_cylce_gas_turbine_efficency": "Not available",
            "combined_cycle_heat_rate": "Not available",
            "open_cycle_heat_rate": "Not available"
        }


def combine_unit_data(stage_results: list, unit_number: str, plant_context: dict) -> dict:
    """Combine all stage results into final unit data structure"""
    
    # Start with required base structure
    combined_data = {
        "sk": f"unit#{plant_context.get('plant_technology', 'coal')}#{unit_number}#plant#{plant_context.get('plant_id', '1')}",
        "unit_number": unit_number,
        "plant_id": plant_context.get('plant_id', '1'),
        # Default values for fields that might be missing

        "pk": plant_context.get('org_uid', 'default null'),  # Use UID as primary key

        # FIXED VALUES as specified
        "annual_operational_hours": 8760,
        "blending_percentage_of_biomass": 0.15,
        "emission_factor_gas": 2.69,

        # Currency-specific units
        "capex_required_renovation_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_retrofit_biomass_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_renovation_open_cycle_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",
        "capex_required_renovation_closed_cycle_unit": f"{get_country_currency(plant_context.get('country', 'Unknown'))}/MW",

        # Other default fields
        "capex_required_renovation": "default null",
        "emission_factor_coal": "default null",
        "emission_factor_of_gas_unit": "default null",
        "emission_factor_unit": "default null",
        "fgds_status": "default null",
        "ramp_down_rate": "default null",
        "ramp_up_rate": "default null"
    }
    
    # Merge all stage results with debugging
    print(f"🔍 Combining {len(stage_results)} stage results for Unit {unit_number}")
    for i, stage_result in enumerate(stage_results):
        if isinstance(stage_result, dict):
            print(f"🔍 Stage {i+1} contributed {len(stage_result)} fields: {list(stage_result.keys())}")
            combined_data.update(stage_result)
        else:
            print(f"⚠️ Stage {i+1} result is not a dict: {type(stage_result)}")

    # Debug: Check if critical time series fields were extracted
    time_series_fields = ["annual_operational_hours", "blending_percentage_of_biomass", "emission_factor_coal"]
    for field in time_series_fields:
        if field in combined_data and combined_data[field] != "default null":
            print(f"✅ {field}: {combined_data[field]}")
        else:
            print(f"⚠️ {field}: Missing or default value")

    return combined_data