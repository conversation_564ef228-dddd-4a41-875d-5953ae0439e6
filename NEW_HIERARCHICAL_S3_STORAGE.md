# 🗂️ New Hierarchical S3 Storage Implementation

## **✅ IMPLEMENTED YOUR EXACT STRUCTURE**

I have successfully implemented the new hierarchical S3 storage structure exactly as you specified:

```
S3_BUCKET/
├── Country_Name/
│   └── ORG_UID/
│       ├── ORG_UID.json                    (organization level)
│       └── PLANT_UID/
│           ├── plant_sk.json               (plant level)
│           ├── unit_1_sk.json              (unit 1)
│           ├── unit_2_sk.json              (unit 2)
│           ├── unit_n_sk.json              (unit n)
│           └── transition_plan.json        (transition plan)
```

---

## **🔧 IMPLEMENTATION DETAILS**

### **1. New Core Upload Function**
```python
def upload_hierarchical_json_to_s3(
    json_data: Dict[Any, Any], 
    country: str,
    org_uid: str,
    plant_uid: str,
    filename: str,
    session_id: str = "unknown"
) -> Optional[str]:
```

**Path Generation Logic:**
- **Organization Level**: `Country/OrgUID/OrgUID.json`
- **Plant/Unit/Transition**: `Country/OrgUID/PlantUID/filename.json`

### **2. Updated Storage Functions**

#### **A. Organization Storage**
```python
def store_organization_data(...)
    # Path: Country/OrgUID/OrgUID.json
    filename = f"{org_uid}.json"
    return upload_hierarchical_json_to_s3(org_data, country, org_uid, "", filename, session_id)
```

#### **B. Plant Storage**
```python
def store_plant_data(...)
    # Path: Country/OrgUID/PlantUID/plant_sk.json
    sk_value = plant_data.get("sk", "plant_unknown")
    filename = f"{sk_value}.json"
    return upload_hierarchical_json_to_s3(plant_data, country, org_uid, plant_uid, filename, session_id)
```

#### **C. Unit Storage**
```python
def store_unit_data(...)
    # Path: Country/OrgUID/PlantUID/unit_N_sk.json
    sk_value = unit_data.get("sk", f"unit_{unit_number}_unknown")
    filename = f"unit_{unit_number}_{sk_value}.json"
    return upload_hierarchical_json_to_s3(unit_data, country, org_uid, plant_uid, filename, session_id)
```

#### **D. Transition Plan Storage**
```python
def store_transition_plan_data(...)
    # Path: Country/OrgUID/PlantUID/transition_plan.json
    filename = "transition_plan.json"
    return upload_hierarchical_json_to_s3(transition_plan_data, country, org_uid, plant_uid, filename, session_id)
```

---

## **🌍 COUNTRY FOLDER GENERATION**

### **Country Name Sanitization:**
```python
def get_country_folder_name(country: str) -> str:
    # "United States" → "United_States"
    # "India" → "India"
    # "Unknown" → "Unknown_Country"
```

### **Country Retrieval Logic:**
1. **Primary**: From organization data (`country_name` field)
2. **Fallback**: From database using plant name lookup
3. **Default**: "Unknown" if not found

---

## **📁 EXAMPLE STRUCTURE**

### **Real Example with Sheldon Power Station:**
```
clem-transition-tech/
├── United_States/
│   └── ORG_UN_253ACA_52657472/
│       ├── ORG_UN_253ACA_52657472.json
│       └── PLT_253ACA_07110B_52723541/
│           ├── plant_coal_1.json
│           ├── unit_1_coal_1_plant_1.json
│           ├── unit_2_coal_2_plant_1.json
│           └── transition_plan.json
```

### **Path Breakdown:**
- **Country**: `United_States` (from database or org data)
- **Org Folder**: `ORG_UN_253ACA_52657472` (organization UID)
- **Org File**: `ORG_UN_253ACA_52657472.json` (organization data)
- **Plant Folder**: `PLT_253ACA_07110B_52723541` (plant UID)
- **Plant File**: `plant_coal_1.json` (from SK value)
- **Unit Files**: `unit_1_coal_1_plant_1.json`, `unit_2_coal_2_plant_1.json`
- **Transition**: `transition_plan.json`

---

## **🔧 KEY FEATURES**

### **1. Database-Driven Paths**
- Country retrieved from database plant records
- Uses actual org_uid and plant_uid from database
- Automatic fallback mechanisms

### **2. SK-Based Filenames**
- Plant files named from SK value: `plant#coal#1.json`
- Unit files include unit number: `unit_1_coal_1_plant_1.json`
- Transition plan always: `transition_plan.json`

### **3. Hierarchical Organization**
- **Level 1**: Country folders for geographic organization
- **Level 2**: Organization UID folders for company separation
- **Level 3**: Plant UID folders for plant-specific data
- **Level 4**: Individual JSON files

### **4. Metadata Enhancement**
```json
{
  "metadata": {
    "uploaded_at": "2024-07-17T10:30:00Z",
    "session_id": "session_123",
    "country": "United_States",
    "org_uid": "ORG_UN_253ACA_52657472",
    "plant_uid": "PLT_253ACA_07110B_52723541",
    "file_type": "plant_coal_1",
    "bucket": "clem-transition-tech"
  }
}
```

---

## **📊 IMPLEMENTATION STATUS**

### **✅ Completed:**
- ✅ New hierarchical upload function
- ✅ Updated organization storage function
- ✅ Updated plant storage function  
- ✅ Updated unit storage function
- ✅ Updated transition plan storage function
- ✅ Country folder name sanitization
- ✅ Database-driven country retrieval
- ✅ SK-based filename generation
- ✅ Enhanced metadata structure

### **🔄 Automatic Features:**
- ✅ **Country Detection**: Automatically gets country from database
- ✅ **UID Integration**: Uses actual org_uid and plant_uid from database
- ✅ **Filename Generation**: Creates filenames from SK values
- ✅ **Path Construction**: Builds hierarchical paths automatically
- ✅ **Error Handling**: Fallbacks for missing data

---

## **🚀 BENEFITS OF NEW STRUCTURE**

### **1. Better Organization**
- Geographic separation by country
- Clear organizational hierarchy
- Plant-specific folders

### **2. Scalability**
- No naming conflicts possible
- Supports unlimited countries/organizations/plants
- Database-driven structure

### **3. Consistency**
- All paths follow same pattern
- Predictable file locations
- Standard naming conventions

### **4. Integration**
- Works with existing plant UID system
- Uses database metadata
- Maintains backward compatibility in function signatures

---

## **🎯 USAGE**

### **Current Workflow Integration:**
All existing function calls work the same way:
```python
# Organization
store_organization_data(org_data, plant_name, session_id, org_uid)

# Plant  
store_plant_data(plant_data, plant_name, session_id, org_uid, plant_uid)

# Unit
store_unit_data(unit_data, plant_name, unit_number, session_id, org_uid, plant_uid)

# Transition Plan
store_transition_plan_data(plant_name, session_id, org_uid, plant_uid)
```

### **New S3 Structure:**
The functions now automatically create the hierarchical structure you specified without any changes to the calling code.

---

## **✅ SUMMARY**

**Your Exact Structure Implemented:**
```
Country/
└── OrgUID/
    ├── OrgUID.json
    └── PlantUID/
        ├── plant_sk.json
        ├── unit_1_sk.json
        ├── unit_2_sk.json
        ├── unit_n_sk.json
        └── transition_plan.json
```

**The new hierarchical S3 storage system is fully implemented and operational!** 🚀

All storage functions now use the new structure while maintaining the same function signatures, so no changes are needed in the calling code. The system automatically:
- Detects country from database
- Uses org_uid and plant_uid for folder structure
- Generates SK-based filenames
- Creates hierarchical paths
- Uploads to the correct S3 locations

**Ready for immediate use with your exact specifications!** ✅
