"""
Test Database Migration

This script tests that the database migration for plant_uuid column works correctly.
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_database_migration():
    """Test that database migration adds plant_uuid column"""
    print("\n🔍 Testing Database Migration")
    print("=" * 60)
    
    try:
        from agent.database_manager import get_database_manager
        
        # Initialize database manager (this should trigger migration)
        print("🔄 Initializing database manager...")
        db_manager = get_database_manager()
        
        # Test that we can generate a plant UUID
        print("🔄 Testing plant UUID generation...")
        plant_uuid = db_manager.generate_plant_uuid()
        print(f"✅ Generated plant UUID: {plant_uuid}")
        
        # Test that we can check plant existence (this was failing before)
        print("🔄 Testing plant existence check...")
        result = db_manager.check_plant_exists("Test Plant")
        print(f"✅ Plant existence check completed: {result}")
        
        # Test that we can generate org UID
        print("🔄 Testing org UID generation...")
        org_uid = db_manager.generate_org_uid("Test Organization", "Test Country")
        print(f"✅ Generated org UID: {org_uid}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database migration test failed: {e}")
        return False

def test_database_connection():
    """Test basic database connection"""
    print("\n🔍 Testing Database Connection")
    print("=" * 60)
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test connection
        if db_manager.test_connection():
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False

def test_column_existence():
    """Test that plant_uuid column exists"""
    print("\n🔍 Testing Column Existence")
    print("=" * 60)
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test column existence check
        exists = db_manager._column_exists('power_plants_registry', 'plant_uuid')
        
        if exists:
            print("✅ plant_uuid column exists")
            return True
        else:
            print("❌ plant_uuid column does not exist")
            return False
            
    except Exception as e:
        print(f"❌ Column existence test failed: {e}")
        return False

def main():
    """Run all database migration tests"""
    print("🚀 Testing Database Migration")
    print("=" * 80)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Column Existence", test_column_existence),
        ("Database Migration", test_database_migration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 DATABASE MIGRATION TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 DATABASE MIGRATION SUCCESSFUL!")
        print("\n💡 What was fixed:")
        print("1. ✅ Database connection working")
        print("2. ✅ plant_uuid column exists")
        print("3. ✅ Migration logic working")
        print("4. ✅ UID generation functions working")
        
        print("\n🚀 Expected behavior:")
        print("- Plant registry checks should work")
        print("- UID generation should succeed")
        print("- No more 'no such column: plant_uuid' errors")
        print("- Database operations should complete successfully")
        
        print("\n🎯 Database migration complete - ready for testing!")
    else:
        print(f"\n⚠️ {total - passed} database migration issues need attention")
        print("\n🔧 Troubleshooting:")
        print("1. Check database file permissions")
        print("2. Verify SQLite installation")
        print("3. Check for database file corruption")
        print("4. Try deleting database file to force recreation")

if __name__ == "__main__":
    main()
