"""
Test Plant UUID as Primary Key

This script tests that the plant UUID functionality is properly implemented:
1. Plant-level JSON uses plant_uuid as "pk" field
2. Unit-level JSON uses plant_uuid as "pk" field  
3. Organization-level JSON still uses org_uid as "pk" field
4. Storage functions accept and use plant_uuid parameter
5. Database retrieval for plant_uuid is implemented
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_storage_function_signatures():
    """Test that storage functions have updated signatures"""
    print("\n🔍 Testing Storage Function Signatures")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            content = f.read()
        
        # Check for updated function signatures
        signature_checks = [
            "def store_plant_data(",
            "plant_uuid: str = None",
            "def store_unit_data(",
            "plant_uuid: str = None",
            "Power Plant UID (primary key for plant level)",
            "Power Plant UID (primary key for unit level)"
        ]
        
        found_signatures = 0
        for check in signature_checks:
            if check in content:
                found_signatures += 1
                print(f"✅ Found: {check}")
            else:
                print(f"❌ Missing: {check}")
        
        if found_signatures >= 5:
            print(f"✅ Storage function signatures updated ({found_signatures}/{len(signature_checks)})")
            return True
        else:
            print(f"❌ Storage function signatures incomplete ({found_signatures}/{len(signature_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Storage function signatures test failed: {e}")
        return False

def test_plant_uuid_usage_in_storage():
    """Test that storage functions use plant_uuid as primary key"""
    print("\n🔍 Testing Plant UUID Usage in Storage")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            content = f.read()
        
        # Check for plant_uuid usage
        usage_checks = [
            "plant_data[\"pk\"] = plant_uuid",
            "unit_data[\"pk\"] = plant_uuid",
            "plant_data[\"plant_uuid\"] = plant_uuid",
            "unit_data[\"plant_uuid\"] = plant_uuid",
            "PLANT LEVEL: Use plant_uuid as primary key",
            "UNIT LEVEL: Use plant_uuid as primary key",
            "Retrieved plant_uuid from database",
            "check_plant_exists(plant_name)"
        ]
        
        found_usage = 0
        for check in usage_checks:
            if check in content:
                found_usage += 1
                print(f"✅ Found: {check}")
            else:
                print(f"❌ Missing: {check}")
        
        if found_usage >= 6:
            print(f"✅ Plant UUID usage implemented ({found_usage}/{len(usage_checks)})")
            return True
        else:
            print(f"❌ Plant UUID usage incomplete ({found_usage}/{len(usage_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Plant UUID usage test failed: {e}")
        return False

def test_graph_storage_calls():
    """Test that graph.py calls storage functions with plant_uuid"""
    print("\n🔍 Testing Graph Storage Calls")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check for updated storage calls
        call_checks = [
            "store_plant_data(formatted_plant_data, plant_name, session_id, org_uid, plant_uuid)",
            "store_unit_data(formatted_unit_data, plant_name, unit_number, session_id, org_uid, plant_uuid)",
            "store_unit_data(formatted_unit_data, plant_name, unit_num, session_id, org_uid, plant_uuid)",
            "Retrieved plant_uuid for storage",
            "Retrieved plant_uuid for unit",
            "check_plant_exists(plant_name)",
            "plant_info.get(\"plant_uuid\")"
        ]
        
        found_calls = 0
        for check in call_checks:
            if check in content:
                found_calls += 1
                print(f"✅ Found: {check}")
            else:
                print(f"❌ Missing: {check}")
        
        if found_calls >= 5:
            print(f"✅ Graph storage calls updated ({found_calls}/{len(call_checks)})")
            return True
        else:
            print(f"❌ Graph storage calls incomplete ({found_calls}/{len(call_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Graph storage calls test failed: {e}")
        return False

def test_database_retrieval_logic():
    """Test that database retrieval logic is implemented"""
    print("\n🔍 Testing Database Retrieval Logic")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            content = f.read()
        
        # Check for database retrieval logic
        retrieval_checks = [
            "from agent.database_manager import get_database_manager",
            "db_manager = get_database_manager()",
            "plant_info = db_manager.check_plant_exists(plant_name)",
            "if plant_info and plant_info.get(\"plant_uuid\"):",
            "plant_uuid = plant_info[\"plant_uuid\"]",
            "Could not retrieve plant_uuid from database"
        ]
        
        found_retrieval = 0
        for check in retrieval_checks:
            if check in content:
                found_retrieval += 1
                print(f"✅ Found: {check}")
            else:
                print(f"❌ Missing: {check}")
        
        if found_retrieval >= 5:
            print(f"✅ Database retrieval logic implemented ({found_retrieval}/{len(retrieval_checks)})")
            return True
        else:
            print(f"❌ Database retrieval logic incomplete ({found_retrieval}/{len(retrieval_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Database retrieval logic test failed: {e}")
        return False

def test_fallback_behavior():
    """Test that fallback behavior is implemented"""
    print("\n🔍 Testing Fallback Behavior")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            content = f.read()
        
        # Check for fallback logic
        fallback_checks = [
            "elif org_uid:",
            "Fallback to org_uid if plant_uuid not available",
            "plant_data[\"pk\"] = org_uid",
            "unit_data[\"pk\"] = org_uid",
            "Using org_uid as fallback",
            "No plant_uuid or org_uid provided"
        ]
        
        found_fallback = 0
        for check in fallback_checks:
            if check in content:
                found_fallback += 1
                print(f"✅ Found: {check}")
            else:
                print(f"❌ Missing: {check}")
        
        if found_fallback >= 5:
            print(f"✅ Fallback behavior implemented ({found_fallback}/{len(fallback_checks)})")
            return True
        else:
            print(f"❌ Fallback behavior incomplete ({found_fallback}/{len(fallback_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Fallback behavior test failed: {e}")
        return False

def main():
    """Run all plant UUID primary key tests"""
    print("🚀 Testing Plant UUID as Primary Key")
    print("=" * 80)
    
    tests = [
        ("Storage Function Signatures", test_storage_function_signatures),
        ("Plant UUID Usage in Storage", test_plant_uuid_usage_in_storage),
        ("Graph Storage Calls", test_graph_storage_calls),
        ("Database Retrieval Logic", test_database_retrieval_logic),
        ("Fallback Behavior", test_fallback_behavior)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 PLANT UUID PRIMARY KEY TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 PLANT UUID PRIMARY KEY SUCCESSFULLY IMPLEMENTED!")
        print("\n💡 What was implemented:")
        print("1. ✅ Updated store_plant_data() to accept plant_uuid parameter")
        print("2. ✅ Updated store_unit_data() to accept plant_uuid parameter")
        print("3. ✅ Plant-level JSON uses plant_uuid as 'pk' field")
        print("4. ✅ Unit-level JSON uses plant_uuid as 'pk' field")
        print("5. ✅ Database retrieval logic for plant_uuid")
        print("6. ✅ Fallback to org_uid if plant_uuid not available")
        print("7. ✅ All storage calls updated in graph.py")
        
        print("\n🚀 Expected JSON structure:")
        print("Organization Level:")
        print('  "pk": "ORG_XX_XXXXXX_XXXXXXXX"  (org_uid)')
        print("\nPlant Level:")
        print('  "pk": "PLANT_UUID_FROM_DATABASE"  (plant_uuid)')
        print('  "org_uid": "ORG_XX_XXXXXX_XXXXXXXX"  (reference)')
        print('  "plant_uuid": "PLANT_UUID_FROM_DATABASE"  (reference)')
        print("\nUnit Level:")
        print('  "pk": "PLANT_UUID_FROM_DATABASE"  (plant_uuid)')
        print('  "org_uid": "ORG_XX_XXXXXX_XXXXXXXX"  (reference)')
        print('  "plant_uuid": "PLANT_UUID_FROM_DATABASE"  (reference)')
        print("\nTransition Plan Level:")
        print('  "pk": "ORG_XX_XXXXXX_XXXXXXXX"  (org_uid)')
        
        print("\n🎯 Plant and unit levels now use plant_uuid as primary key!")
    else:
        print(f"\n⚠️ {total - passed} plant UUID implementations need attention")

if __name__ == "__main__":
    main()
