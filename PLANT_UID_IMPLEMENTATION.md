# 🔧 Plant UID Implementation - Complete Database and JSON Updates

## **✅ Successfully Implemented Plant UID System**

I have completely implemented the plant UID system as requested, updating both the database structure and JSON generation to use plant UIDs as primary keys.

---

## **🎯 Requirements Fulfilled**

### **1. ✅ Added Plant UID to Database**
- **Added `plant_uid` column** to `PowerPlantRegistry` table
- **Generated unique plant UIDs** for all plants
- **Updated all database methods** to include plant_uid

### **2. ✅ Updated JSON Primary Keys**
- **Plant Level JSON**: `pk` field now uses `plant_uid` instead of `org_uid`
- **Unit Level JSON**: `pk` field now uses `plant_uid` instead of `org_uid`
- **Maintained `org_uid`** for reference and organization linking

---

## **🔧 Database Changes**

### **A. Database Schema Update**
**File:** `backend/src/agent/database_manager.py`

```python
# ADDED: Plant UID column to database model
class PowerPlantRegistry(Base):
    # ... existing fields ...
    org_uid = Column(String(50), nullable=False, index=True)
    plant_uid = Column(String(50), nullable=True, index=True)  # NEW: Plant-specific UID
    plant_status = Column(Enum(PlantStatus), default=PlantStatus.OPERATIONAL)
```

### **B. Plant UID Generation Function**
```python
def generate_plant_uid(self, plant_name: str, org_uid: str) -> str:
    """
    Generate unique plant UID
    
    Format: PLT_{ORG_HASH}_{PLANT_HASH}_{TIMESTAMP}
    Example: PLT_A7B2C9_D4E5F6_20241201
    """
    # Extract org hash from org_uid
    org_parts = org_uid.split('_')
    org_hash = org_parts[2] if len(org_parts) >= 3 else "UNKNOWN"
    
    # Create hash from plant name
    plant_hash = hashlib.sha256(plant_name.lower().encode()).hexdigest()[:6].upper()
    
    # Add timestamp for uniqueness
    timestamp = str(int(time.time()))[-8:]
    
    plant_uid = f"PLT_{org_hash}_{plant_hash}_{timestamp}"
    return plant_uid
```

### **C. Migration Function for Existing Plants**
```python
def generate_plant_uids_for_existing_plants(self) -> bool:
    """Generate plant UIDs for existing plants that don't have them"""
    # Gets all plants without plant_uid and generates UIDs for them
    # Updates database with new plant UIDs
    # Returns True if successful
```

### **D. Updated Database Methods**
All database methods now return `plant_uid`:
- `check_plant_exists()` - Returns plant_uid
- `get_plants_by_org_uid()` - Returns plant_uid for each plant
- `get_all_plants()` - Returns plant_uid for each plant
- `save_organization_plants()` - Generates and saves plant_uid

---

## **🔧 JSON Generation Changes**

### **A. Plant Level JSON Updates**
**File:** `backend/src/agent/json_s3_storage.py`

**BEFORE (using org_uid as pk):**
```python
def store_plant_data(plant_data, plant_name, session_id, org_uid):
    if org_uid:
        plant_data["pk"] = org_uid  # ❌ Wrong: org_uid as pk
```

**AFTER (using plant_uid as pk):**
```python
def store_plant_data(plant_data, plant_name, session_id, org_uid, plant_uid):
    if plant_uid:
        plant_data["pk"] = plant_uid  # ✅ Correct: plant_uid as pk
        plant_data["plant_uid"] = plant_uid
        plant_data["org_uid"] = org_uid  # Keep for reference
```

### **B. Unit Level JSON Updates**
**File:** `backend/src/agent/json_s3_storage.py`

**BEFORE (using org_uid as pk):**
```python
def store_unit_data(unit_data, plant_name, unit_number, session_id, org_uid):
    if org_uid:
        unit_data["pk"] = org_uid  # ❌ Wrong: org_uid as pk
```

**AFTER (using plant_uid as pk):**
```python
def store_unit_data(unit_data, plant_name, unit_number, session_id, org_uid, plant_uid):
    if plant_uid:
        unit_data["pk"] = plant_uid  # ✅ Correct: plant_uid as pk
        unit_data["plant_uid"] = plant_uid
        unit_data["org_uid"] = org_uid  # Keep for reference
```

### **C. Unit Extraction Updates**
**File:** `backend/src/agent/unit_extraction_stages.py`

**BEFORE:**
```python
"pk": plant_context.get('org_uid', 'default null'),  # ❌ Wrong
```

**AFTER:**
```python
"pk": plant_context.get('plant_uid', plant_context.get('org_uid', 'default null')),  # ✅ Correct
```

---

## **🔧 Plant Data Processing Updates**

### **A. Plant Data Formatting**
**File:** `backend/src/agent/graph.py`

```python
def process_plant_data_formatting(plant_data, session_id, org_uid):
    # NEW: Get plant_uid from database and use as pk
    plant_name = plant_data.get("name", "")
    plant_uid = None
    
    if plant_name and org_uid:
        db_manager = get_database_manager()
        existing_plant = db_manager.check_plant_exists(plant_name)
        if existing_plant and existing_plant.get("plant_uid"):
            plant_uid = existing_plant["plant_uid"]
        else:
            plant_uid = db_manager.generate_plant_uid(plant_name, org_uid)
    
    # Set pk to plant_uid (not org_uid)
    if plant_uid:
        plant_data["pk"] = plant_uid
        plant_data["plant_uid"] = plant_uid
```

### **B. Plant Context Updates**
```python
# Updated plant_context to include plant_uid
plant_context = {
    "plant_name": plant_name,
    "plant_technology": plant_data.get("plant_type", "coal"),
    "plant_id": str(plant_data.get("plant_id", "1")),
    "country": "Unknown",
    "org_uid": state.get("org_uid", ""),      # Organization UID
    "plant_uid": plant_data.get("plant_uid", "")  # Plant UID for pk field
}
```

---

## **🔧 Storage Function Updates**

### **A. All Storage Calls Updated**
```python
# Plant data storage
plant_s3_url = store_plant_data(formatted_plant_data, plant_name, session_id, org_uid, plant_uid)

# Unit data storage  
unit_s3_url = store_unit_data(formatted_unit_data, plant_name, unit_num, session_id, org_uid, plant_uid)
```

### **B. Database Plant Saving**
```python
# When saving new plants, generate plant_uid
plant_uid = self.generate_plant_uid(plant_name, org_uid)

plant_record = PowerPlantRegistry(
    org_name=org_name,
    plant_name=plant_name,
    country=country,
    org_uid=org_uid,
    plant_uid=plant_uid,  # NEW: Include plant UID
    # ... other fields
)
```

---

## **🔧 Migration and Testing**

### **A. Updated Check Script**
**File:** `check_db.py`

```python
def migrate_plant_uids():
    """Generate plant UIDs for existing plants that don't have them"""
    db_manager = get_database_manager()
    if hasattr(db_manager, 'generate_plant_uids_for_existing_plants'):
        result = db_manager.generate_plant_uids_for_existing_plants()
        # Migrates all existing plants to have plant UIDs
```

### **B. Database Verification**
- Script now shows both `org_uid` and `plant_uid` for each plant
- Verifies that all plants have unique plant UIDs
- Runs migration automatically when checking database

---

## **🚀 Expected Results**

### **Database Structure:**
```
PowerPlantRegistry Table:
├── org_uid: "ORG_US_A7B2C9_52657472"     (Organization identifier)
├── plant_uid: "PLT_A7B2C9_D4E5F6_20241201" (Plant identifier - NEW)
├── plant_name: "Sheldon Power Station"
└── ... other fields
```

### **Plant Level JSON:**
```json
{
  "pk": "PLT_A7B2C9_D4E5F6_20241201",        // ✅ plant_uid as pk
  "plant_uid": "PLT_A7B2C9_D4E5F6_20241201", // ✅ plant_uid field
  "org_uid": "ORG_US_A7B2C9_52657472",       // ✅ org_uid for reference
  "name": "Sheldon Power Station",
  // ... other plant fields
}
```

### **Unit Level JSON:**
```json
{
  "pk": "PLT_A7B2C9_D4E5F6_20241201",        // ✅ plant_uid as pk
  "plant_uid": "PLT_A7B2C9_D4E5F6_20241201", // ✅ plant_uid field
  "org_uid": "ORG_US_A7B2C9_52657472",       // ✅ org_uid for reference
  "unit_number": "1",
  // ... other unit fields
}
```

---

## **📊 Files Modified**

### **Database Layer:**
- `backend/src/agent/database_manager.py` - Added plant_uid column and methods
- `check_db.py` - Added migration function

### **JSON Generation:**
- `backend/src/agent/json_s3_storage.py` - Updated pk field logic
- `backend/src/agent/unit_extraction_stages.py` - Updated pk field
- `backend/src/agent/graph.py` - Updated plant data processing and storage calls

### **Key Functions Updated:**
- `store_plant_data()` - Now uses plant_uid as pk
- `store_unit_data()` - Now uses plant_uid as pk
- `process_plant_data_formatting()` - Gets plant_uid from database
- `combine_unit_data()` - Uses plant_uid from context

---

## **🎯 Summary**

**✅ Database Changes:**
- Added `plant_uid` column to database
- Created plant UID generation function
- Updated all database methods to include plant_uid
- Added migration for existing plants

**✅ JSON Changes:**
- Plant level JSON now uses `plant_uid` as `pk`
- Unit level JSON now uses `plant_uid` as `pk`
- Both levels maintain `org_uid` for reference
- All storage functions updated to pass plant_uid

**✅ Data Flow:**
- Plant UID generated when saving plants to database
- Plant UID retrieved when processing plant data
- Plant UID passed through plant_context to unit extraction
- Plant UID used as primary key in all JSON files

**The plant UID system is now fully implemented and operational!** 🚀
